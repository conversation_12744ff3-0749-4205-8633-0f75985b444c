# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt
*.ckpt

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

<<<<<<< HEAD
# Data files (keep structure but not large files)
*.mp4
*.avi
*.mov
*.mkv
*.wav
*.mp3

# Model checkpoints and logs
models/*.pt
models/*.pth
logs/
runs/
wandb/

# Jupyter Notebooks
.ipynb_checkpoints/
*.ipynb

# Temporary files
*.tmp
*.temp
*.log

# Cache
.cache/
.pytest_cache/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Environments
.env
.venv
=======
# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# Expo
.expo/
.expo-shared/

# Machine Learning Models (large files)
*.h5
*.pkl
*.joblib
models/*.h5
models/*.pkl

# Data files (can be large)
data/raw/
data/processed/
*.csv
*.json
*.mp4
*.avi
*.mov

# Temporary files
tmp/
temp/
>>>>>>> 341bf11eb13e42c932a34316811e4e8459f555a1
