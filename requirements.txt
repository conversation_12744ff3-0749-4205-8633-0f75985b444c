# Core ML and Deep Learning - PyTorch (Primary)
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Computer Vision and Image Processing
opencv-python>=4.8.0
Pillow>=10.0.0

# Data Science and Analysis
pandas>=1.5.0
numpy>=1.24.0
scikit-learn>=1.3.0
scipy>=1.10.0

# Visualization and Plotting
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Progress and Utilities
tqdm>=4.65.0
pyyaml>=6.0
colorama>=0.4.6
rich>=13.0.0

# Video Processing
imageio>=2.31.0
imageio-ffmpeg>=0.4.8

# Configuration and Logging
hydra-core>=1.3.0
wandb>=0.15.0
tensorboard>=2.13.0

# Performance and Memory
psutil>=5.9.0
torchmetrics>=0.11.0

# Web Frameworks
fastapi>=0.100.0
uvicorn>=0.22.0
python-multipart>=0.0.6
flask>=2.3.0
flask-cors>=4.0.0

# Model Export and Deployment
onnx>=1.14.0
onnxruntime>=1.15.0

# Audio/Video Processing
ffmpeg-python>=0.2.0

# Development and Visualization
jupyter>=1.0.0
python-dotenv>=1.0.0

# Testing and Quality
pytest>=7.4.0
pytest-cov>=4.1.0

# Development Tools
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
