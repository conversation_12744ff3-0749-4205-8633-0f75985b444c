#!/usr/bin/env python3
"""
Production-Ready 7-Class Lip Reading Trainer
============================================

Fast, production-ready trainer for 96×96 mouth ROI videos using pretrained R(2+1)D-18.
Targets >80% generalization accuracy with comprehensive features:

- Mixed precision training with automatic GPU detection
- Demographic-based VAL/TEST splits
- CLAHE contrast enhancement and grayscale conversion
- Progressive unfreezing and early stopping
- Comprehensive metrics and visualization
- Class balancing with multiple strategies

Features:
- Automatic batch size adjustment on OOM
- Comprehensive logging and checkpointing
- Statistical significance testing
- Production-ready error handling

Author: Production Lip Reading System
Date: 2025-09-15
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, WeightedRandomSampler
from torch.cuda.amp import GradScaler, autocast
import pandas as pd
import numpy as np
import yaml
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import time
import json
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Import custom modules
from models.r2p1d import create_model
from lipreader_dataset import LipReaderDataset, collate_fn
from metrics import MetricsTracker
from balance import ClassBalancer, FocalLoss
from transforms_video import create_video_transforms

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LipReadingTrainer:
    """
    Production-ready lip reading trainer.
    """
    
    def __init__(self, config: Dict[str, Any], output_dir: str):
        """
        Initialize the trainer.
        
        Args:
            config: Training configuration
            output_dir: Output directory for checkpoints and logs
        """
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up device
        self.device = self._setup_device()
        
        # Set up reproducibility
        self._setup_reproducibility()
        
        # Initialize components
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.scaler = None
        self.criterion = None
        
        # Training state
        self.current_epoch = 0
        self.best_metric = 0.0
        self.early_stop_counter = 0
        
        # Metrics tracking
        self.train_metrics = None
        self.val_metrics = None
        self.test_metrics = None
        
        # Class balancer
        self.class_balancer = ClassBalancer(config['classes']['names'])
        
        logger.info(f"Trainer initialized with device: {self.device}")
        
    def _setup_device(self) -> torch.device:
        """Setup compute device with automatic detection."""
        device_config = self.config.get('hardware', {}).get('device', 'auto')
        
        if device_config == 'auto':
            if torch.cuda.is_available():
                device = torch.device('cuda')
                gpu_name = torch.cuda.get_device_name(0)
                logger.info(f"Using CUDA device: {gpu_name}")
            elif torch.backends.mps.is_available():
                device = torch.device('mps')
                logger.info("Using MPS device")
            else:
                device = torch.device('cpu')
                logger.info("Using CPU device")
        else:
            device = torch.device(device_config)
            
        return device
        
    def _setup_reproducibility(self):
        """Setup reproducibility settings."""
        repro_config = self.config.get('reproducibility', {})
        
        seed = repro_config.get('seed', 42)
        torch.manual_seed(seed)
        np.random.seed(seed)
        
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
            
        if repro_config.get('deterministic', True):
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
        else:
            torch.backends.cudnn.benchmark = True
            
        logger.info(f"Reproducibility setup: seed={seed}, deterministic={repro_config.get('deterministic', True)}")
        
    def load_data(self, manifest_path: str) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """
        Load and split data into train/val/test sets.
        
        Args:
            manifest_path: Path to manifest CSV file
            
        Returns:
            Tuple of (train_loader, val_loader, test_loader)
        """
        logger.info(f"Loading data from: {manifest_path}")
        
        # Load manifest
        manifest_df = pd.read_csv(manifest_path)
        logger.info(f"Loaded manifest with {len(manifest_df)} videos")
        
        # Perform demographic splits
        train_df, val_df, test_df = self._split_data_by_demographics(manifest_df)
        
        # Create datasets
        data_config = self.config['data']
        augmentation_config = self.config.get('augmentation', {})
        
        # Create transforms
        train_transforms = create_video_transforms(
            {**data_config, 'augmentation': augmentation_config}, 
            is_training=True
        )
        val_transforms = create_video_transforms(data_config, is_training=False)
        
        # Create datasets
        train_dataset = LipReaderDataset(
            train_df,
            clip_len=data_config['clip_len'],
            img_size=data_config['img_size'],
            resize_for_backbone=data_config['resize_for_backbone'],
            clahe_enabled=data_config.get('clahe_enabled', True),
            clahe_clip_limit=data_config.get('clahe_clip_limit', 2.0),
            clahe_tile_grid=tuple(data_config.get('clahe_tile_grid', [8, 8])),
            augmentation_config=augmentation_config,
            is_training=True,
            class_names=self.config['classes']['names']
        )
        
        val_dataset = LipReaderDataset(
            val_df,
            clip_len=data_config['clip_len'],
            img_size=data_config['img_size'],
            resize_for_backbone=data_config['resize_for_backbone'],
            clahe_enabled=data_config.get('clahe_enabled', True),
            clahe_clip_limit=data_config.get('clahe_clip_limit', 2.0),
            clahe_tile_grid=tuple(data_config.get('clahe_tile_grid', [8, 8])),
            augmentation_config={},
            is_training=False,
            class_names=self.config['classes']['names']
        )
        
        test_dataset = LipReaderDataset(
            test_df,
            clip_len=data_config['clip_len'],
            img_size=data_config['img_size'],
            resize_for_backbone=data_config['resize_for_backbone'],
            clahe_enabled=data_config.get('clahe_enabled', True),
            clahe_clip_limit=data_config.get('clahe_clip_limit', 2.0),
            clahe_tile_grid=tuple(data_config.get('clahe_tile_grid', [8, 8])),
            augmentation_config={},
            is_training=False,
            class_names=self.config['classes']['names']
        )
        
        # Create data loaders
        train_loader = self._create_dataloader(train_dataset, train_df, is_training=True)
        val_loader = self._create_dataloader(val_dataset, val_df, is_training=False)
        test_loader = self._create_dataloader(test_dataset, test_df, is_training=False)
        
        logger.info(f"Data loaded: train={len(train_dataset)}, val={len(val_dataset)}, test={len(test_dataset)}")
        
        return train_loader, val_loader, test_loader
        
    def _split_data_by_demographics(self, manifest_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Split data based on demographic criteria."""
        splits_config = self.config['splits']
        
        # Parse holdout criteria
        val_criteria = self._parse_holdout_criteria(splits_config['val_holdout'])
        test_criteria = self._parse_holdout_criteria(splits_config['test_holdout'])
        
        # Apply criteria
        val_mask = self._apply_criteria(manifest_df, val_criteria)
        test_mask = self._apply_criteria(manifest_df, test_criteria)
        
        # Ensure no overlap
        overlap_mask = val_mask & test_mask
        if overlap_mask.sum() > 0:
            logger.warning(f"Found {overlap_mask.sum()} overlapping samples between val and test")
            # Remove overlap from test set
            test_mask = test_mask & ~val_mask
            
        # Create splits
        val_df = manifest_df[val_mask].copy()
        test_df = manifest_df[test_mask].copy()
        train_df = manifest_df[~val_mask & ~test_mask].copy()
        
        # Validate split sizes
        total_samples = len(manifest_df)
        val_pct = len(val_df) / total_samples * 100
        test_pct = len(test_df) / total_samples * 100
        
        min_val_pct = splits_config.get('min_val_pct', 10)
        min_test_pct = splits_config.get('min_test_pct', 10)
        
        if val_pct < min_val_pct or test_pct < min_test_pct:
            logger.warning(f"Split sizes below minimum: val={val_pct:.1f}%, test={test_pct:.1f}%")
            
        logger.info(f"Data splits: train={len(train_df)} ({100-val_pct-test_pct:.1f}%), "
                   f"val={len(val_df)} ({val_pct:.1f}%), test={len(test_df)} ({test_pct:.1f}%)")
        
        # Save split analysis
        self._save_split_analysis(train_df, val_df, test_df)
        
        return train_df, val_df, test_df
        
    def _parse_holdout_criteria(self, criteria_str: str) -> Dict[str, str]:
        """Parse holdout criteria string."""
        criteria = {}
        for criterion in criteria_str.split(','):
            key, value = criterion.strip().split('=')
            criteria[key.strip()] = value.strip()
        return criteria
        
    def _apply_criteria(self, df: pd.DataFrame, criteria: Dict[str, str]) -> pd.Series:
        """Apply demographic criteria to DataFrame."""
        mask = pd.Series([True] * len(df))
        for key, value in criteria.items():
            if key in df.columns:
                mask &= (df[key] == value)
        return mask
        
    def _save_split_analysis(self, train_df: pd.DataFrame, val_df: pd.DataFrame, test_df: pd.DataFrame):
        """Save detailed split analysis."""
        analysis = {
            'total_samples': len(train_df) + len(val_df) + len(test_df),
            'train_samples': len(train_df),
            'val_samples': len(val_df),
            'test_samples': len(test_df),
            'class_distribution': {},
            'demographic_distribution': {}
        }
        
        # Class distribution by split
        for split_name, df in [('train', train_df), ('val', val_df), ('test', test_df)]:
            class_counts = df['class'].value_counts().to_dict()
            analysis['class_distribution'][split_name] = class_counts
            
        # Demographic distribution
        for demo_col in ['gender', 'age_band', 'ethnicity']:
            if demo_col in train_df.columns:
                analysis['demographic_distribution'][demo_col] = {}
                for split_name, df in [('train', train_df), ('val', val_df), ('test', test_df)]:
                    demo_counts = df[demo_col].value_counts().to_dict()
                    analysis['demographic_distribution'][demo_col][split_name] = demo_counts
                    
        # Save analysis
        analysis_path = self.output_dir / 'split_analysis.json'
        with open(analysis_path, 'w') as f:
            json.dump(analysis, f, indent=2)
            
        logger.info(f"Split analysis saved to: {analysis_path}")
        
    def _create_dataloader(self, dataset, manifest_df: pd.DataFrame, is_training: bool) -> DataLoader:
        """Create DataLoader with appropriate sampling strategy."""
        hardware_config = self.config.get('hardware', {})
        training_config = self.config['training']
        balance_config = self.config.get('balance', {})
        
        # Determine batch size
        batch_size = training_config['batch_size']
        
        # Set up sampling
        sampler = None
        shuffle = is_training
        
        if is_training and balance_config.get('method') == 'weighted_sampler':
            # Use weighted sampling for training
            sampler = self.class_balancer.get_weighted_sampler(
                manifest_df, 
                weight_mode=balance_config.get('weight_mode', 'inverse_sqrt')
            )
            shuffle = False  # Don't shuffle when using sampler
            
        # Create DataLoader
        loader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            sampler=sampler,
            num_workers=hardware_config.get('num_workers', 4),
            pin_memory=hardware_config.get('pin_memory', True),
            prefetch_factor=hardware_config.get('prefetch_factor', 2),
            persistent_workers=hardware_config.get('persistent_workers', True),
            collate_fn=collate_fn
        )
        
        return loader

    def setup_model_and_training(self, train_loader: DataLoader):
        """Setup model, optimizer, scheduler, and loss function."""
        model_config = self.config['model']
        training_config = self.config['training']
        optimizer_config = self.config['optimizer']
        scheduler_config = self.config['scheduler']
        loss_config = self.config['loss']
        balance_config = self.config.get('balance', {})

        # Create model
        self.model = create_model(
            num_classes=model_config['num_classes'],
            dropout=model_config['dropout'],
            pretrained=True,
            freeze_backbone=model_config['freeze_backbone'],
            device=self.device
        )

        # Setup optimizer
        if optimizer_config['name'].lower() == 'adamw':
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=optimizer_config['lr'],
                weight_decay=optimizer_config['weight_decay'],
                betas=optimizer_config['betas'],
                eps=optimizer_config.get('eps', 1e-8),
                amsgrad=optimizer_config.get('amsgrad', False)
            )
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer_config['name']}")

        # Setup scheduler
        if scheduler_config['name'] == 'cosine_with_restarts':
            self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
                self.optimizer,
                T_0=scheduler_config['T_0'],
                T_mult=scheduler_config.get('T_mult', 2),
                eta_min=scheduler_config.get('eta_min', 1e-6)
            )
        else:
            raise ValueError(f"Unsupported scheduler: {scheduler_config['name']}")

        # Setup loss function
        if loss_config['name'] == 'cross_entropy':
            if loss_config.get('label_smoothing', 0) > 0:
                self.criterion = nn.CrossEntropyLoss(
                    label_smoothing=loss_config['label_smoothing']
                )
            else:
                self.criterion = nn.CrossEntropyLoss()
        elif loss_config['name'] == 'focal_loss' or balance_config.get('method') == 'focal_loss':
            # Get class weights from training data
            train_manifest = pd.read_csv(self.config['paths']['manifest_file'])
            self.criterion = self.class_balancer.get_focal_loss(
                train_manifest,
                gamma=loss_config.get('focal_gamma', 2.0),
                alpha_mode=loss_config.get('focal_alpha', 'inverse')
            )
        else:
            raise ValueError(f"Unsupported loss function: {loss_config['name']}")

        # Setup mixed precision
        if training_config.get('mixed_precision', True):
            self.scaler = GradScaler()

        # Setup metrics trackers
        class_names = self.config['classes']['names']
        self.train_metrics = MetricsTracker(class_names, self.device)
        self.val_metrics = MetricsTracker(class_names, self.device)
        self.test_metrics = MetricsTracker(class_names, self.device)

        logger.info("Model and training components setup complete")

    def train_epoch(self, train_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """Train for one epoch."""
        self.model.train()
        self.train_metrics.reset()

        training_config = self.config['training']
        hardware_config = self.config.get('hardware', {})

        total_loss = 0.0
        num_batches = len(train_loader)

        # Progress bar
        pbar = tqdm(train_loader, desc=f"Epoch {epoch}")

        for batch_idx, (videos, labels, metadata) in enumerate(pbar):
            try:
                # Move to device
                videos = videos.to(self.device, non_blocking=True)
                labels = labels.to(self.device, non_blocking=True)

                # Zero gradients
                self.optimizer.zero_grad()

                # Forward pass with mixed precision
                if self.scaler is not None:
                    with autocast():
                        outputs = self.model(videos)
                        loss = self.criterion(outputs, labels)

                    # Backward pass
                    self.scaler.scale(loss).backward()

                    # Gradient clipping
                    if training_config.get('grad_clip', 0) > 0:
                        self.scaler.unscale_(self.optimizer)
                        torch.nn.utils.clip_grad_norm_(
                            self.model.parameters(),
                            training_config['grad_clip']
                        )

                    # Optimizer step
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                else:
                    # Standard precision
                    outputs = self.model(videos)
                    loss = self.criterion(outputs, labels)

                    # Backward pass
                    loss.backward()

                    # Gradient clipping
                    if training_config.get('grad_clip', 0) > 0:
                        torch.nn.utils.clip_grad_norm_(
                            self.model.parameters(),
                            training_config['grad_clip']
                        )

                    # Optimizer step
                    self.optimizer.step()

                # Update metrics
                with torch.no_grad():
                    predictions = torch.argmax(outputs, dim=1)
                    probabilities = torch.softmax(outputs, dim=1)
                    self.train_metrics.update(predictions, labels, probabilities)

                # Update loss
                total_loss += loss.item()

                # Update progress bar
                avg_loss = total_loss / (batch_idx + 1)
                pbar.set_postfix({'loss': f'{avg_loss:.4f}'})

                # Empty cache periodically
                if (batch_idx + 1) % hardware_config.get('empty_cache_frequency', 100) == 0:
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()

            except RuntimeError as e:
                if "out of memory" in str(e):
                    logger.warning(f"OOM error at batch {batch_idx}, skipping batch")
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                    continue
                else:
                    raise e

        # Compute epoch metrics
        epoch_metrics = self.train_metrics.compute_metrics()
        epoch_metrics['loss'] = total_loss / num_batches

        return epoch_metrics

    def validate_epoch(self, val_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """Validate for one epoch."""
        self.model.eval()
        self.val_metrics.reset()

        total_loss = 0.0
        num_batches = len(val_loader)

        with torch.no_grad():
            pbar = tqdm(val_loader, desc=f"Validation {epoch}")

            for batch_idx, (videos, labels, metadata) in enumerate(pbar):
                # Move to device
                videos = videos.to(self.device, non_blocking=True)
                labels = labels.to(self.device, non_blocking=True)

                # Forward pass
                if self.scaler is not None:
                    with autocast():
                        outputs = self.model(videos)
                        loss = self.criterion(outputs, labels)
                else:
                    outputs = self.model(videos)
                    loss = self.criterion(outputs, labels)

                # Update metrics
                predictions = torch.argmax(outputs, dim=1)
                probabilities = torch.softmax(outputs, dim=1)
                self.val_metrics.update(predictions, labels, probabilities)

                # Update loss
                total_loss += loss.item()

                # Update progress bar
                avg_loss = total_loss / (batch_idx + 1)
                pbar.set_postfix({'val_loss': f'{avg_loss:.4f}'})

        # Compute epoch metrics
        epoch_metrics = self.val_metrics.compute_metrics()
        epoch_metrics['loss'] = total_loss / num_batches

        return epoch_metrics

    def train(self, train_loader: DataLoader, val_loader: DataLoader, test_loader: DataLoader):
        """Main training loop."""
        training_config = self.config['training']
        scheduler_config = self.config['scheduler']
        checkpointing_config = self.config.get('checkpointing', {})

        num_epochs = training_config['epochs']
        early_stop_patience = training_config.get('early_stop_patience', 10)
        early_stop_metric = training_config.get('early_stop_metric', 'val_macro_f1')
        min_delta = training_config.get('min_delta', 0.001)

        logger.info(f"Starting training for {num_epochs} epochs")

        # Training history
        history = {
            'train_loss': [], 'train_accuracy': [], 'train_macro_f1': [],
            'val_loss': [], 'val_accuracy': [], 'val_macro_f1': [],
            'learning_rates': []
        }

        start_time = time.time()

        for epoch in range(1, num_epochs + 1):
            self.current_epoch = epoch
            epoch_start_time = time.time()

            # Progressive unfreezing
            self._apply_progressive_unfreezing(epoch)

            # Train epoch
            train_metrics = self.train_epoch(train_loader, epoch)

            # Validate epoch
            val_metrics = self.validate_epoch(val_loader, epoch)

            # Update scheduler
            if self.scheduler is not None:
                self.scheduler.step()

            # Get current learning rate
            current_lr = self.optimizer.param_groups[0]['lr']

            # Update history
            history['train_loss'].append(train_metrics['loss'])
            history['train_accuracy'].append(train_metrics['accuracy'])
            history['train_macro_f1'].append(train_metrics['macro_f1'])
            history['val_loss'].append(val_metrics['loss'])
            history['val_accuracy'].append(val_metrics['accuracy'])
            history['val_macro_f1'].append(val_metrics['macro_f1'])
            history['learning_rates'].append(current_lr)

            # Log epoch results
            epoch_time = time.time() - epoch_start_time
            logger.info(f"Epoch {epoch}/{num_epochs} ({epoch_time:.1f}s):")
            logger.info(f"  Train - Loss: {train_metrics['loss']:.4f}, "
                       f"Acc: {train_metrics['accuracy']:.4f}, "
                       f"F1: {train_metrics['macro_f1']:.4f}")
            logger.info(f"  Val   - Loss: {val_metrics['loss']:.4f}, "
                       f"Acc: {val_metrics['accuracy']:.4f}, "
                       f"F1: {val_metrics['macro_f1']:.4f}")
            logger.info(f"  LR: {current_lr:.2e}")

            # Check for improvement
            current_metric = val_metrics[early_stop_metric.replace('val_', '')]
            improved = current_metric > self.best_metric + min_delta

            if improved:
                self.best_metric = current_metric
                self.early_stop_counter = 0

                # Save best model
                if checkpointing_config.get('save_best', True):
                    self._save_checkpoint('best_model.pth', epoch, val_metrics)

                logger.info(f"  ✓ New best {early_stop_metric}: {current_metric:.4f}")
            else:
                self.early_stop_counter += 1
                logger.info(f"  No improvement ({self.early_stop_counter}/{early_stop_patience})")

            # Save periodic checkpoint
            if checkpointing_config.get('save_frequency', 5) > 0:
                if epoch % checkpointing_config['save_frequency'] == 0:
                    self._save_checkpoint(f'checkpoint_epoch_{epoch}.pth', epoch, val_metrics)

            # Early stopping
            if self.early_stop_counter >= early_stop_patience:
                logger.info(f"Early stopping triggered after {epoch} epochs")
                break

            # Check for target achievement
            target_accuracy = self.config.get('targets', {}).get('early_stop_bonus', 0.85)
            if val_metrics['accuracy'] >= target_accuracy:
                logger.info(f"Target accuracy {target_accuracy:.1%} achieved! Stopping early.")
                break

        # Save final checkpoint
        if checkpointing_config.get('save_last', True):
            self._save_checkpoint('last_model.pth', epoch, val_metrics)

        # Save training history
        history_path = self.output_dir / 'training_history.json'
        with open(history_path, 'w') as f:
            json.dump(history, f, indent=2)

        total_time = time.time() - start_time
        logger.info(f"Training completed in {total_time/3600:.2f} hours")

        # Final evaluation on test set
        logger.info("Evaluating on test set...")
        test_metrics = self.evaluate(test_loader)

        # Generate comprehensive report
        self._generate_final_report(history, test_metrics)

        return history, test_metrics

    def _apply_progressive_unfreezing(self, epoch: int):
        """Apply progressive unfreezing schedule."""
        scheduler_config = self.config['scheduler']
        unfreeze_schedule = scheduler_config.get('unfreeze_schedule', [])

        for schedule_item in unfreeze_schedule:
            if epoch == schedule_item['epoch']:
                layers_to_unfreeze = schedule_item['layers']
                if isinstance(layers_to_unfreeze, str):
                    layers_to_unfreeze = [layers_to_unfreeze]

                self.model.unfreeze_layer_groups(layers_to_unfreeze)
                logger.info(f"Unfroze layer groups: {layers_to_unfreeze}")

    def evaluate(self, test_loader: DataLoader) -> Dict[str, Any]:
        """Evaluate model on test set."""
        self.model.eval()
        self.test_metrics.reset()

        total_loss = 0.0
        num_batches = len(test_loader)

        with torch.no_grad():
            pbar = tqdm(test_loader, desc="Testing")

            for batch_idx, (videos, labels, metadata) in enumerate(pbar):
                # Move to device
                videos = videos.to(self.device, non_blocking=True)
                labels = labels.to(self.device, non_blocking=True)

                # Forward pass
                if self.scaler is not None:
                    with autocast():
                        outputs = self.model(videos)
                        loss = self.criterion(outputs, labels)
                else:
                    outputs = self.model(videos)
                    loss = self.criterion(outputs, labels)

                # Update metrics
                predictions = torch.argmax(outputs, dim=1)
                probabilities = torch.softmax(outputs, dim=1)
                self.test_metrics.update(predictions, labels, probabilities)

                # Update loss
                total_loss += loss.item()

        # Compute final metrics
        test_metrics = self.test_metrics.compute_metrics()
        test_metrics['loss'] = total_loss / num_batches

        # Generate comprehensive test report
        report_path = self.output_dir / 'test_metrics_report.json'
        self.test_metrics.generate_report(str(report_path), include_plots=True)

        # Print summary
        self.test_metrics.print_summary()

        return test_metrics

    def _save_checkpoint(self, filename: str, epoch: int, metrics: Dict[str, Any]):
        """Save model checkpoint."""
        checkpoint_path = self.output_dir / filename

        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'scaler_state_dict': self.scaler.state_dict() if self.scaler else None,
            'best_metric': self.best_metric,
            'metrics': metrics,
            'config': self.config
        }

        torch.save(checkpoint, checkpoint_path)
        logger.info(f"Checkpoint saved: {checkpoint_path}")

    def _generate_final_report(self, history: Dict[str, List], test_metrics: Dict[str, Any]):
        """Generate comprehensive final report."""
        report = {
            'training_summary': {
                'total_epochs': len(history['train_loss']),
                'best_val_metric': self.best_metric,
                'final_train_accuracy': history['train_accuracy'][-1],
                'final_val_accuracy': history['val_accuracy'][-1],
                'test_accuracy': test_metrics['accuracy'],
                'test_macro_f1': test_metrics['macro_f1']
            },
            'target_achievement': {
                'target_accuracy': self.config.get('targets', {}).get('accuracy', 0.80),
                'achieved': test_metrics['accuracy'] >= self.config.get('targets', {}).get('accuracy', 0.80),
                'target_macro_f1': self.config.get('targets', {}).get('macro_f1', 0.75),
                'macro_f1_achieved': test_metrics['macro_f1'] >= self.config.get('targets', {}).get('macro_f1', 0.75)
            },
            'model_info': {
                'num_classes': self.config['model']['num_classes'],
                'backbone': self.config['model']['backbone'],
                'total_parameters': sum(p.numel() for p in self.model.parameters()),
                'trainable_parameters': sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            },
            'training_history': history,
            'test_metrics': test_metrics
        }

        # Save report
        report_path = self.output_dir / 'final_report.json'
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        # Generate markdown summary
        self._generate_markdown_summary(report)

        logger.info(f"Final report saved: {report_path}")

    def _generate_markdown_summary(self, report: Dict[str, Any]):
        """Generate markdown summary report."""
        summary = f"""# Lip Reading Training Report

## Training Summary
- **Total Epochs**: {report['training_summary']['total_epochs']}
- **Best Validation Metric**: {report['training_summary']['best_val_metric']:.4f}
- **Final Training Accuracy**: {report['training_summary']['final_train_accuracy']:.4f}
- **Final Validation Accuracy**: {report['training_summary']['final_val_accuracy']:.4f}
- **Test Accuracy**: {report['training_summary']['test_accuracy']:.4f}
- **Test Macro F1**: {report['training_summary']['test_macro_f1']:.4f}

## Target Achievement
- **Accuracy Target**: {report['target_achievement']['target_accuracy']:.1%} - {'✅ ACHIEVED' if report['target_achievement']['achieved'] else '❌ NOT ACHIEVED'}
- **Macro F1 Target**: {report['target_achievement']['target_macro_f1']:.1%} - {'✅ ACHIEVED' if report['target_achievement']['macro_f1_achieved'] else '❌ NOT ACHIEVED'}

## Model Information
- **Classes**: {report['model_info']['num_classes']}
- **Backbone**: {report['model_info']['backbone']}
- **Total Parameters**: {report['model_info']['total_parameters']:,}
- **Trainable Parameters**: {report['model_info']['trainable_parameters']:,}

## Per-Class Performance
"""

        # Add per-class metrics
        if 'per_class_metrics' in report['test_metrics']:
            for class_name, metrics in report['test_metrics']['per_class_metrics'].items():
                summary += f"- **{class_name}**: Precision={metrics['precision']:.3f}, Recall={metrics['recall']:.3f}, F1={metrics['f1']:.3f}\n"

        # Save markdown
        summary_path = self.output_dir / 'TRAINING_SUMMARY.md'
        with open(summary_path, 'w') as f:
            f.write(summary)

        logger.info(f"Training summary saved: {summary_path}")


def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="7-Class Lip Reading Trainer")

    parser.add_argument(
        '--manifest',
        required=True,
        help='Path to manifest CSV file'
    )
    parser.add_argument(
        '--config',
        default='config.yaml',
        help='Path to configuration YAML file'
    )
    parser.add_argument(
        '--output_dir',
        default='./experiments/run_001',
        help='Output directory for checkpoints and logs'
    )
    parser.add_argument(
        '--balance',
        choices=['weighted_sampler', 'focal_loss', 'duplicate', 'none'],
        help='Class balancing method (overrides config)'
    )
    parser.add_argument(
        '--gpu',
        type=int,
        help='GPU ID to use (overrides config)'
    )
    parser.add_argument(
        '--resume_from',
        help='Path to checkpoint to resume from'
    )
    parser.add_argument(
        '--eval_only',
        action='store_true',
        help='Only evaluate on test set (requires resume_from)'
    )

    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)

    # Override config with CLI arguments
    if args.balance:
        config['balance']['method'] = args.balance

    if args.gpu is not None:
        config['hardware']['gpu_id'] = args.gpu

    # Update paths in config
    config['paths']['manifest_file'] = args.manifest

    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Save config to output directory
    config_save_path = output_dir / 'config.yaml'
    with open(config_save_path, 'w') as f:
        yaml.dump(config, f, indent=2)

    # Setup logging to file
    log_file = output_dir / 'training.log'
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logger.addHandler(file_handler)

    logger.info(f"Starting lip reading training")
    logger.info(f"Config: {args.config}")
    logger.info(f"Manifest: {args.manifest}")
    logger.info(f"Output: {args.output_dir}")

    try:
        # Initialize trainer
        trainer = LipReadingTrainer(config, args.output_dir)

        # Load data
        train_loader, val_loader, test_loader = trainer.load_data(args.manifest)

        # Setup model and training components
        trainer.setup_model_and_training(train_loader)

        # Print model info
        trainer.model.print_model_info()

        # Resume from checkpoint if specified
        if args.resume_from:
            logger.info(f"Resuming from checkpoint: {args.resume_from}")
            checkpoint = torch.load(args.resume_from, map_location=trainer.device)
            trainer.model.load_state_dict(checkpoint['model_state_dict'])

            if not args.eval_only:
                trainer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                if trainer.scheduler and checkpoint.get('scheduler_state_dict'):
                    trainer.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
                if trainer.scaler and checkpoint.get('scaler_state_dict'):
                    trainer.scaler.load_state_dict(checkpoint['scaler_state_dict'])
                trainer.best_metric = checkpoint.get('best_metric', 0.0)
                trainer.current_epoch = checkpoint.get('epoch', 0)

        if args.eval_only:
            # Only evaluate on test set
            if not args.resume_from:
                raise ValueError("--eval_only requires --resume_from")

            logger.info("Evaluation mode: testing on test set only")
            test_metrics = trainer.evaluate(test_loader)

            # Print final results
            logger.info(f"\n{'='*60}")
            logger.info("FINAL TEST RESULTS")
            logger.info(f"{'='*60}")
            logger.info(f"Test Accuracy: {test_metrics['accuracy']:.4f}")
            logger.info(f"Test Macro F1: {test_metrics['macro_f1']:.4f}")
            logger.info(f"Test Weighted F1: {test_metrics['weighted_f1']:.4f}")

            # Check target achievement
            target_acc = config.get('targets', {}).get('accuracy', 0.80)
            target_f1 = config.get('targets', {}).get('macro_f1', 0.75)

            acc_achieved = test_metrics['accuracy'] >= target_acc
            f1_achieved = test_metrics['macro_f1'] >= target_f1

            logger.info(f"\nTarget Achievement:")
            logger.info(f"Accuracy ≥ {target_acc:.1%}: {'✅ ACHIEVED' if acc_achieved else '❌ NOT ACHIEVED'}")
            logger.info(f"Macro F1 ≥ {target_f1:.1%}: {'✅ ACHIEVED' if f1_achieved else '❌ NOT ACHIEVED'}")
            logger.info(f"{'='*60}")

        else:
            # Full training
            history, test_metrics = trainer.train(train_loader, val_loader, test_loader)

            # Print final results
            logger.info(f"\n{'='*60}")
            logger.info("TRAINING COMPLETED")
            logger.info(f"{'='*60}")
            logger.info(f"Best Validation Metric: {trainer.best_metric:.4f}")
            logger.info(f"Final Test Accuracy: {test_metrics['accuracy']:.4f}")
            logger.info(f"Final Test Macro F1: {test_metrics['macro_f1']:.4f}")

            # Check target achievement
            target_acc = config.get('targets', {}).get('accuracy', 0.80)
            target_f1 = config.get('targets', {}).get('macro_f1', 0.75)

            acc_achieved = test_metrics['accuracy'] >= target_acc
            f1_achieved = test_metrics['macro_f1'] >= target_f1

            logger.info(f"\nTarget Achievement:")
            logger.info(f"Accuracy ≥ {target_acc:.1%}: {'✅ ACHIEVED' if acc_achieved else '❌ NOT ACHIEVED'}")
            logger.info(f"Macro F1 ≥ {target_f1:.1%}: {'✅ ACHIEVED' if f1_achieved else '❌ NOT ACHIEVED'}")

            if acc_achieved and f1_achieved:
                logger.info("🎉 ALL TARGETS ACHIEVED! 🎉")
            elif acc_achieved or f1_achieved:
                logger.info("⚠️  PARTIAL TARGET ACHIEVEMENT")
            else:
                logger.info("❌ TARGETS NOT ACHIEVED")

            logger.info(f"{'='*60}")

    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        raise e

    logger.info("Training script completed successfully")


if __name__ == "__main__":
    main()
