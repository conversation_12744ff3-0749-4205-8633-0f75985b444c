#!/usr/bin/env python3
"""
Simple manifest creator for visual validation.
Creates a basic manifest from expanded_cropped_dataset directory.
"""

import os
import pandas as pd
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_simple_manifest():
    """Create a simple manifest for visual validation."""
    
    expanded_dir = Path("./expanded_cropped_dataset")
    if not expanded_dir.exists():
        logger.error("expanded_cropped_dataset directory not found")
        return
        
    video_files = list(expanded_dir.glob("*.mp4"))
    logger.info(f"Found {len(video_files)} video files")
    
    if len(video_files) == 0:
        logger.error("No video files found")
        return
        
    manifest_data = []
    
    for video_path in video_files:
        filename = video_path.name
        
        # Extract class from filename
        class_name = "unknown"
        if "doctor" in filename:
            class_name = "doctor"
        elif "help" in filename:
            class_name = "help"
        elif "glasses" in filename:
            class_name = "glasses"
        elif "phone" in filename:
            class_name = "phone"
        elif "pillow" in filename:
            class_name = "pillow"
        elif "my_mouth_is_dry" in filename:
            class_name = "my_mouth_is_dry"
        elif "water" in filename:
            class_name = "water"
            
        # Extract demographics
        gender = "unknown"
        age_band = "unknown"
        ethnicity = "unknown"
        
        if "__male__" in filename:
            gender = "male"
        elif "__female__" in filename:
            gender = "female"
            
        if "__18to39__" in filename:
            age_band = "18-39"
        elif "__40to64__" in filename:
            age_band = "40-64"
        elif "__65plus__" in filename:
            age_band = "65+"
            
        if "__caucasian__" in filename:
            ethnicity = "caucasian"
        elif "__asian__" in filename:
            ethnicity = "asian"
        elif "__aboriginal__" in filename:
            ethnicity = "aboriginal"
        elif "__african__" in filename:
            ethnicity = "african"
            
        manifest_data.append({
            'path': str(video_path),
            'class': class_name,
            'gender': gender,
            'age_band': age_band,
            'ethnicity': ethnicity,
            'source': 'expanded_cropped_dataset',
            'processed_version': 'cropped'
        })
        
    # Create DataFrame
    df = pd.DataFrame(manifest_data)
    
    # Save manifest
    output_path = "manifest.csv"
    df.to_csv(output_path, index=False)
    logger.info(f"Created manifest with {len(df)} videos: {output_path}")
    
    # Show stats
    logger.info(f"Classes: {df['class'].value_counts().to_dict()}")
    logger.info(f"Gender: {df['gender'].value_counts().to_dict()}")
    logger.info(f"Age bands: {df['age_band'].value_counts().to_dict()}")
    
if __name__ == "__main__":
    create_simple_manifest()
