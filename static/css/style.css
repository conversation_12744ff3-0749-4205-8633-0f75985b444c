/* Mobile-First Responsive Design for Lipreading App */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 1rem;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #4a5568;
}

header p {
    font-size: 1rem;
    color: #666;
}

/* Camera Section */
.camera-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.video-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    margin: 0 auto 1.5rem;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

#video {
    width: 100%;
    height: auto;
    display: block;
    transform: scaleX(-1); /* Mirror effect for selfie view */
}

/* Controls */
.controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.btn-record {
    background: linear-gradient(45deg, #f44336, #d32f2f);
    color: white;
}

.btn-record:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4);
}

.btn-stop {
    background: linear-gradient(45deg, #ff9800, #f57c00);
    color: white;
}

.btn-stop:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.4);
}

/* Status */
.status {
    text-align: center;
}

.status-text {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 1rem;
}

.countdown {
    font-size: 2rem;
    font-weight: bold;
    color: #f44336;
    min-height: 2.5rem;
}

/* Results Section */
.results-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.results h3, .error h3 {
    margin-bottom: 1rem;
    color: #4a5568;
    text-align: center;
}

.prediction {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.predicted-word, .confidence {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.label {
    font-weight: 600;
    color: #666;
}

.word {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4CAF50;
    text-transform: uppercase;
}

.percentage {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2196F3;
}

/* Probability Bars */
.all-probabilities h4 {
    margin-bottom: 1rem;
    color: #4a5568;
}

.probability-bar {
    margin-bottom: 0.8rem;
}

.probability-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
}

.probability-word {
    font-weight: 600;
    text-transform: uppercase;
}

.probability-value {
    color: #666;
}

.probability-fill {
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.probability-fill-inner {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #2196F3);
    border-radius: 4px;
    transition: width 0.5s ease;
}

/* Error */
.error {
    background: #ffebee;
    border: 1px solid #ffcdd2;
    color: #c62828;
    text-align: center;
}

.error p {
    margin: 0;
}

/* Hidden class */
.hidden {
    display: none !important;
}

/* Footer */
footer {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.instructions h3, .tips h4 {
    color: #4a5568;
    margin-bottom: 1rem;
}

.instructions ol, .tips ul {
    padding-left: 1.5rem;
    color: #666;
}

.instructions li, .tips li {
    margin-bottom: 0.5rem;
}

.tips {
    margin-top: 1.5rem;
}

/* Tablet and Desktop */
@media (min-width: 768px) {
    .container {
        max-width: 800px;
        padding: 2rem;
    }
    
    .controls {
        flex-direction: row;
        justify-content: center;
    }
    
    .btn {
        flex: 1;
        max-width: 200px;
    }
    
    .prediction {
        flex-direction: row;
    }
    
    .predicted-word, .confidence {
        flex: 1;
    }
}

@media (min-width: 1024px) {
    header h1 {
        font-size: 2.5rem;
    }
    
    .video-container {
        max-width: 500px;
    }
}
