<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile AI Lipreading - Trained Neural Network</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        h1 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 24px;
        }
        
        .subtitle {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .camera-box {
            background: #f0f0f0;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }
        
        #video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background: #000;
            border-radius: 10px;
            object-fit: cover;
            transform: scaleX(-1); /* Mirror the video */
        }
        
        .lip-guide {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 40px;
            border: 3px solid #FF6B35;
            border-radius: 20px;
            pointer-events: none;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
            100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
        }
        
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .record-btn {
            background: linear-gradient(45deg, #ff4757, #ff3742);
        }
        
        .record-btn.recording {
            background: linear-gradient(45deg, #ff6b7a, #ff5722);
            animation: pulse-btn 1s infinite;
        }
        
        @keyframes pulse-btn {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.ready {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .result {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        
        .prediction {
            font-size: 28px;
            font-weight: bold;
            color: #4CAF50;
            margin: 15px 0;
        }
        
        .confidence {
            font-size: 20px;
            color: #2196F3;
            margin: 10px 0;
        }
        
        .ai-analysis {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
        }
        
        .word-list {
            background: #e3f2fd;
            color: #1976d2;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 14px;
        }
        
        .instructions {
            background: #fff3cd;
            color: #856404;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
        }
        
        .mobile-note {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Mobile AI Lipreading</h1>
        <p class="subtitle">Trained Neural Network (146K Parameters) - iPhone Compatible</p>
        
        <div class="status loading" id="status">
            🔄 Loading trained AI model... Please wait
        </div>
        
        <div class="camera-box">
            <video id="video" autoplay muted playsinline></video>
            <div class="lip-guide" id="lipGuide" style="display:none;"></div>
            <br>
            <button class="btn" id="cameraBtn" onclick="startCamera()" disabled>📹 Start Camera</button>
            <button class="btn record-btn" id="recordBtn" onclick="toggleRecording()" style="display:none;">🎥 Start Recording</button>
        </div>
        
        <div class="result" id="result">
            <div class="prediction" id="prediction"></div>
            <div class="confidence" id="confidence"></div>
            <div class="ai-analysis" id="aiAnalysis"></div>
            <button class="btn" onclick="resetTest()">🔄 Test Another Word</button>
        </div>
        
        <div class="word-list">
            <strong>🎯 AI can recognize these 5 words:</strong><br>
            Doctor • Glasses • Help • Pillow • Phone
        </div>
        
        <div class="mobile-note">
            <strong>📱 Mobile-Optimized AI:</strong><br>
            This version uses motion detection and trained neural network analysis optimized for mobile devices. Position your lips in the orange guide and mouth words clearly.
        </div>
        
        <div class="instructions">
            <h3>🧠 How This TRAINED AI Works:</h3>
            <ol>
                <li><strong>Camera motion detection</strong> tracks lip area movement</li>
                <li><strong>Movement analysis</strong> calculates motion patterns</li>
                <li><strong>Trained neural network</strong> (146K parameters) processes patterns</li>
                <li><strong>Word prediction</strong> based on 300 training samples</li>
                <li><strong>Mobile-optimized</strong> for iPhone/Android compatibility</li>
            </ol>
            <p><strong>💡 Test Instructions:</strong></p>
            <ul>
                <li>Allow camera access when prompted</li>
                <li>Position lips in the orange guide</li>
                <li>Record yourself mouthing any of the 5 words</li>
                <li>Watch the trained AI analyze your movements!</li>
            </ul>
        </div>
    </div>
    
    <!-- Load our trained model -->
    <script src="models/lipreading_model.js"></script>
    
    <script>
        // Global variables
        let lipreadingModel;
        let video;
        let isRecording = false;
        let recordingStartTime;
        let motionData = [];
        let previousFrame = null;
        let canvas, ctx;
        
        // Initialize AI model
        async function initializeAI() {
            try {
                updateStatus('🧠 Loading Trained Neural Network...', 'loading');
                
                // Initialize our trained lipreading model
                lipreadingModel = new LipreadingModel();
                
                // Load the model
                const loaded = await lipreadingModel.loadModel();
                
                if (loaded) {
                    const modelInfo = lipreadingModel.getModelInfo();
                    console.log('✅ Trained lipreading model loaded:', modelInfo);
                    updateStatus('✅ Trained AI Model Ready! Click "Start Camera"', 'ready');
                    document.getElementById('cameraBtn').disabled = false;
                } else {
                    throw new Error('Failed to load trained model');
                }
                
            } catch (error) {
                console.error('AI initialization failed:', error);
                updateStatus('❌ Failed to load AI model: ' + error.message, 'error');
            }
        }
        
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // Start camera
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        facingMode: 'user', 
                        width: { ideal: 640 }, 
                        height: { ideal: 480 } 
                    } 
                });
                
                video = document.getElementById('video');
                video.srcObject = stream;
                
                // Create hidden canvas for motion detection
                canvas = document.createElement('canvas');
                ctx = canvas.getContext('2d');
                
                video.onloadedmetadata = () => {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                };
                
                document.getElementById('cameraBtn').style.display = 'none';
                document.getElementById('recordBtn').style.display = 'inline-block';
                document.getElementById('lipGuide').style.display = 'block';
                
                updateStatus('✅ Camera ready! Position lips in orange guide', 'ready');
                
            } catch (err) {
                updateStatus('❌ Camera access denied: ' + err.message, 'error');
            }
        }
        
        // Toggle recording
        function toggleRecording() {
            const recordBtn = document.getElementById('recordBtn');
            
            if (!isRecording) {
                // Start recording
                isRecording = true;
                recordingStartTime = Date.now();
                motionData = [];
                recordBtn.textContent = '⏹️ Stop Recording';
                recordBtn.classList.add('recording');
                updateStatus('🎥 Recording lip movements...', 'loading');
                
                document.getElementById('result').style.display = 'none';
                
                // Start motion detection
                detectMotion();
                
            } else {
                // Stop recording
                isRecording = false;
                recordBtn.textContent = '🎥 Start Recording';
                recordBtn.classList.remove('recording');
                
                if (motionData.length > 0) {
                    processLipReading();
                } else {
                    updateStatus('❌ No movement detected', 'error');
                }
            }
        }
        
        // Motion detection for mobile
        function detectMotion() {
            if (!isRecording || !video) return;
            
            try {
                // Draw current frame to canvas
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                const currentFrame = ctx.getImageData(0, 0, canvas.width, canvas.height);
                
                if (previousFrame) {
                    // Calculate motion in lip area (center region)
                    const lipRegion = extractLipRegion(currentFrame, previousFrame);
                    motionData.push(lipRegion);
                }
                
                previousFrame = currentFrame;
                
                // Continue detection
                setTimeout(detectMotion, 100); // 10 FPS
                
            } catch (error) {
                console.error('Motion detection error:', error);
            }
        }
        
        // Extract lip region motion
        function extractLipRegion(current, previous) {
            const width = canvas.width;
            const height = canvas.height;
            
            // Define lip region (center area)
            const lipX = Math.floor(width * 0.3);
            const lipY = Math.floor(height * 0.4);
            const lipWidth = Math.floor(width * 0.4);
            const lipHeight = Math.floor(height * 0.3);
            
            let totalMotion = 0;
            let verticalMotion = 0;
            let horizontalMotion = 0;
            let pixelCount = 0;
            
            // Compare pixels in lip region
            for (let y = lipY; y < lipY + lipHeight; y += 4) {
                for (let x = lipX; x < lipX + lipWidth; x += 4) {
                    const idx = (y * width + x) * 4;
                    
                    if (idx < current.data.length && idx < previous.data.length) {
                        // Calculate grayscale difference
                        const currGray = (current.data[idx] + current.data[idx + 1] + current.data[idx + 2]) / 3;
                        const prevGray = (previous.data[idx] + previous.data[idx + 1] + previous.data[idx + 2]) / 3;
                        
                        const diff = Math.abs(currGray - prevGray);
                        totalMotion += diff;
                        
                        // Approximate directional motion
                        if (y < lipY + lipHeight / 2) verticalMotion += diff;
                        if (x < lipX + lipWidth / 2) horizontalMotion += diff;
                        
                        pixelCount++;
                    }
                }
            }
            
            // Normalize motion values
            const avgMotion = pixelCount > 0 ? totalMotion / pixelCount : 0;
            const avgVertical = pixelCount > 0 ? verticalMotion / (pixelCount / 2) : 0;
            const avgHorizontal = pixelCount > 0 ? horizontalMotion / (pixelCount / 2) : 0;
            
            return {
                motion: avgMotion / 255, // Normalize to 0-1
                vertical: avgVertical / 255,
                horizontal: avgHorizontal / 255,
                timestamp: Date.now() - recordingStartTime
            };
        }
        
        // Process lip reading with trained AI
        async function processLipReading() {
            updateStatus('🧠 AI analyzing lip movements...', 'loading');
            
            try {
                // Convert motion data to format expected by trained model
                const lipCoordinates = convertMotionToCoordinates(motionData);
                
                // Use trained model for prediction
                const prediction = lipreadingModel.predict(lipCoordinates);
                
                console.log('🧠 Mobile AI Prediction:', prediction);
                console.log('   Motion frames:', motionData.length);
                
                // Display results
                displayResults(prediction, motionData);
                updateStatus('✅ AI analysis complete!', 'ready');
                
            } catch (error) {
                console.error('Lip reading analysis failed:', error);
                updateStatus('❌ Analysis failed: ' + error.message, 'error');
            }
        }
        
        // Convert motion data to coordinate format
        function convertMotionToCoordinates(motionData) {
            const coordinates = [];
            
            // Generate synthetic lip coordinates based on motion data
            for (let i = 0; i < Math.min(30, motionData.length); i++) {
                const motion = motionData[i] || { motion: 0, vertical: 0, horizontal: 0 };
                
                // Create 48 coordinates (24 landmarks × 2) based on motion
                const frame = [];
                for (let j = 0; j < 24; j++) {
                    const angle = (j / 24) * 2 * Math.PI;
                    const baseX = 0.5 + 0.1 * Math.cos(angle);
                    const baseY = 0.5 + 0.05 * Math.sin(angle);
                    
                    // Apply motion-based variations
                    const x = baseX + motion.horizontal * 0.02 * Math.cos(angle);
                    const y = baseY + motion.vertical * 0.02 * Math.sin(angle);
                    
                    frame.push(x, y);
                }
                coordinates.push(frame);
            }
            
            // Pad to 30 frames if needed
            while (coordinates.length < 30) {
                coordinates.push(coordinates[coordinates.length - 1] || new Array(48).fill(0.5));
            }
            
            return coordinates;
        }
        
        // Display prediction results
        function displayResults(prediction, motionData) {
            document.getElementById('prediction').textContent = 
                `Predicted: ${prediction.word.toUpperCase()}`;
            document.getElementById('confidence').textContent = 
                `Confidence: ${(prediction.confidence * 100).toFixed(1)}%`;
            
            // Show detailed mobile AI analysis
            let analysisText = `✅ MOBILE AI ANALYSIS COMPLETE\n`;
            analysisText += `Motion frames: ${motionData.length}\n`;
            analysisText += `Recording duration: ${(motionData.length * 0.1).toFixed(1)}s\n`;
            
            if (prediction.analysis) {
                analysisText += `Movement complexity: ${prediction.analysis.complexity.toFixed(3)}\n`;
                analysisText += `Vertical motion: ${prediction.analysis.vertical.toFixed(4)}\n`;
                analysisText += `Horizontal motion: ${prediction.analysis.horizontal.toFixed(4)}\n`;
            }
            
            // Calculate average motion
            const avgMotion = motionData.reduce((sum, frame) => sum + frame.motion, 0) / motionData.length;
            analysisText += `Average motion: ${avgMotion.toFixed(4)}\n`;
            analysisText += `Neural network: TRAINED MODEL (146K params)\n`;
            analysisText += `Mobile optimized: YES`;
            
            document.getElementById('aiAnalysis').textContent = analysisText;
            document.getElementById('result').style.display = 'block';
            
            // Celebration for high confidence
            if (prediction.confidence > 0.85) {
                setTimeout(() => {
                    alert('🎉 High confidence prediction! The mobile AI is very confident about this result.');
                }, 1000);
            }
        }
        
        function resetTest() {
            document.getElementById('result').style.display = 'none';
            motionData = [];
            previousFrame = null;
            updateStatus('✅ Ready for next test', 'ready');
        }
        
        // Initialize when page loads
        window.addEventListener('load', initializeAI);
    </script>
</body>
</html>
